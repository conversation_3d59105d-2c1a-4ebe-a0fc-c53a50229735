# MWLT Density Prediction Pipeline — Findings and Differences

This report documents the complete density (DEN) prediction pipeline in the MWLT codebase (code/), and highlights key differences from the VP (sonic velocity) pipeline.

## 1) Training Entry Point
- Path: `code/train.py`
- Entry: `main(args)` (invoked via the CLI under `if __name__ == "__main__":`)
- Responsibilities:
  - Builds `WellDataset` for training/validation
  - Selects and instantiates `MWLT_{Small|Base|Large}`
  - Uses Adam optimizer and MSE loss
  - Early stopping; best checkpoint saved to `best_model.pth`

Common invocation example:
- `python code/train.py --model_type base --train_files_path ../data_normal/train --val_files_path ../data_normal/val`

## 2) Model Architecture (Density)
- Path: `code/model.py`
- Key components:
  - `Input_Embedding` (Conv1d + `ResCNN` blocks)
  - `TransformerBlock` (stack of `TransformerEncoder` with MHA + FFN)
  - `Decoder` (Conv1d + `Sigmoid` for density “normal” pipeline)
  - `MWL_Transformer` wires embedding → transformer → decoder
  - Factory functions: `MWLT_Small`, `MWLT_Base`, `MWLT_Large`

Notes:
- The `Decoder` applies `Sigmoid`, implying targets are expected to be normalized to [0, 1] in the training data (preprocessing expected upstream in the HDF5s).

## 3) Inference/Prediction Entry Point
- Path: `code/test.py`
- Entry: `main(args)`
- Responsibilities:
  - Loads `WellDataset` for test files
  - Builds the same `MWLT_*` variant, loads checkpoint via `utils.load_checkpoint`
  - Runs forward pass, writes per-file HDF5 with datasets: `real`, `pred`

Outputs:
- Default save path: `../result_base1/pred_val`
- Post-processing/visualization: `code/plot_density_results.py` produces analysis plots and a summary report from the HDF5 outputs

## 4) Data Pipeline (Training and Inference)
- Path: `code/dataset.py`
- Class: `WellDataset`
- Expected input data:
  - Directory of HDF5 files with curve datasets named by curve (e.g., `GR`, `AC`, `CNL`, `RLLD`, `DEN`)
  - Shape per curve: sequences of length `total_seqlen` (default 720)
- Behavior:
  - Transforms arrays to tensors with shape `[C, L]` (later batched to `[B, C, L]`)
  - If `transform=True`, performs random crop to `effect_seqlen` (default 640); otherwise selects the first 640 samples

Default paths used by scripts:
- Training: `../data_normal/train` (train), `../data_normal/val` (val)
- Testing: `../data_normal/val` (test)

## 5) Key Configuration (Density)
From `code/train.py` defaults:
- Input curves: `GR, AC, CNL, RLLD`
- Output curves: `DEN`
- Model variants: `small | base | large`
- Sequence: `total_seqlen=720` → `effect_seqlen=640` (crop)
- Batch size: `32`
- Learning rate: `1e-5`
- Dropouts: `drop=0.1`, `attn_drop=0.1`, `position_drop=0.1`
- Epochs: `2000`
- Early stopping patience: `150`
- Checkpoints: best model saved to `<save_path>/best_model.pth`

From `code/test.py` defaults:
- `checkpoint_path=../result_base_normal/best_model.pth`
- `save_path=../result_base1/pred_val`
- `transform=False` (no random cropping during inference)

## 6) Differences vs. VP (Sonic Velocity) Pipeline
- Output head behavior:
  - Density: `Decoder` uses `Sigmoid` (bounded output), relies on pre-normalized DEN targets in data
  - VP (improved): removes `Sigmoid` and outputs normalized values directly; denormalization is applied explicitly at inference (to μs/ft)

- Scripts and locations:
  - Density training/inference: `code/train.py`, `code/test.py`, plotting in `code/plot_density_results.py`
  - VP (improved) training/inference: `code/train_vp_improved.py`, `code/test_vp_improved.py`, with visualization enhancements

- Normalization handling:
  - Density: implicit expectation that training data HDF5s store normalized `DEN` compatible with the `Sigmoid` head
  - VP (improved): normalization/denormalization explicitly managed in the VP-specific stack (no artificial bounds)

- Targets and units:
  - Density: values typically compared in the normalized space produced/expected by the dataset; plotted vs. actual directly from HDF5
  - VP (improved): predictions are denormalized to physical units (μs/ft) for evaluation/plots

## 7) End-to-End Flow Summary (Density)
1. Prepare HDF5 training/validation sets with `GR, AC, CNL, RLLD` inputs and normalized `DEN` target
2. Train via `code/train.py` (select Small/Base/Large); best model saved with early stopping
3. Run `code/test.py` on target HDF5s; per-file `real` and `pred` stored in HDF5
4. Optionally visualize with `code/plot_density_results.py` to generate plots and a summary report

---
Generated on: 2025-08-31

