# Developer Guide

This guide is for contributors and maintainers working on MWLT.

## Quick Start
```bash
cd code
run_test.bat
```

## Train
```bash
# Density
python train.py --model_type base --epochs 2000 --batch_size 64 --save_path ../result_base_normal

# Improved Vp
python train_vp_improved.py
# or
python vp_model_improved.py --mode train --model_type base
```

## Test
```bash
# Density
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth"

# Improved Vp
python test_vp_improved.py
python vp_model_improved.py --mode predict --model_path model.pth --input_file A1.hdf5
```

## Utilities
```bash
python test_device_detection.py
python check_data_files.py
python plot_density_results.py
```

## Data
- Formats: HDF5, LAS
- Sequence: 720 points → 640 effective
- Normalization: curve-specific

## Structure
- code/: core sources (model, dataset, training, utils, vp_model_improved)
- data_*/: datasets
- result_*/: training outputs
- vp_improved_*/: improved Vp artifacts

## Hardware
- GPU recommended; CPU supported

## Notes
- Improved Vp removes sigmoid constraints; train on normalized targets; denormalize at inference
- Prefer improved scripts over legacy *vp* scripts

See also:
- docs/integration-guide.md
- docs/vp-transformer-issues-and-fixes.md
- docs/enhanced-plotting-guide.md
- docs/codebase-index.md

