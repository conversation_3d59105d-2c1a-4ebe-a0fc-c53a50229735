# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Machine Learning Well Log Transformer (MWLT) project for predicting well log properties using transformer-based deep learning models. The system primarily focuses on density (DEN) prediction from input curves (GR, AC, CNL, RLLD) and includes **significantly improved** sonic velocity (Vp/AC) prediction with proper scaling and enhanced visualization capabilities.

## Core Architecture

### Model Components (code/model.py)
- **Input_Embedding**: ResNet-based feature extraction with 1D convolutions
- **MWLT Models**: Three transformer variants (Small, Base, Large) with different capacities:
  - Small: 2 ResNet blocks, 2 transformer encoders, 2 attention heads, 64 features
  - Base: 4 ResNet blocks, 4 transformer encoders, 4 attention heads, 64 features  
  - Large: 6 ResNet blocks, 6 transformer encoders, 8 attention heads, 128 features
- **Architecture**: Input curves → ResNet feature extraction → Transformer encoders → Decoder → Output curves

### Data Pipeline (code/dataset.py)
- **WellDataset**: Handles HDF5 well log files with sliding window approach
- **Input/Output**: 4 input curves → 1-3 output curves, sequence length 720→640 with augmentation
- **Data Format**: HDF5 files containing curve arrays (GR, AC, CNL, RLLD, DEN)

## Development Commands

### **🚀 Quick Start (Recommended)**
```bash
# Interactive test suite with all capabilities
cd code
run_test.bat  # Choose from 7 options including improved Vp training/testing
```

### Training Models
```bash
# Main density prediction training
cd code
python train.py --model_type base --epochs 2000 --batch_size 64 --save_path ../result_base_normal

# 🆕 IMPROVED Vp prediction training (Recommended)
python train_vp_improved.py  # Uses better architecture with proper scaling
python vp_model_improved.py --mode train --model_type base  # Alternative interface

# Legacy Vp training (deprecated)
python simple_train_vp.py                    # Quick test (30 epochs)
python train_vp_model.py --model_type base   # Full training
python train_vp_transfer.py --pretrained_model_path ../result_base_normal/best_model.pth
```

### Testing and Evaluation
```bash
# Density prediction testing with enhanced visualization
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth"
python plot_density_results.py  # Create enhanced density plots

# 🆕 IMPROVED Vp prediction testing (Recommended)
python test_vp_improved.py  # Auto data detection, proper scaling, visualization
python vp_model_improved.py --mode predict --model_path model.pth --input_file A1.hdf5

# System utilities
python test_device_detection.py  # Check GPU/CPU capabilities
python check_data_files.py       # Verify data file availability
```

### Data Processing
```bash
# LAS file processing
python las_processor.py

# Prepare Vp training data
python prepare_vp_data.py

# Create sample data
python create_sample_data.py
```

## Directory Structure

### Data Organization
- `data_normal/`: Normalized training data (train/val splits)
- `data_nonormal/`: Non-normalized training data
- `data_vp_prediction/`: Prepared data for sonic velocity prediction
- `las_test_data/`: Sample LAS and HDF5 well files
- `simple_vp_data/`: Simple Vp training datasets

### Model Results
- `result_base_normal/`: Base model results with normalization
- `result_*_*/`: Different model size and normalization combinations
- `vp_improved_training/`: **🆕 Improved Vp model results** (recommended)
- `simple_vp_training/`: Legacy Vp prediction model results
- `vp_prediction_results/`: Final Vp prediction outputs
- `density_prediction_results/`: Enhanced density prediction visualizations
- `test_plots/`: Generated visualization plots

### Code Structure
- `model.py`: Core transformer architecture
- `train.py`: Main training script for density prediction
- `test.py`: Model evaluation and inference for density
- `dataset.py`: Data loading and preprocessing
- `utils.py`: Utilities including GPU/CPU auto-detection
- `vp_model_improved.py`: **🆕 Improved Vp prediction module** (complete pipeline)
- `train_vp_improved.py`: **🆕 Improved Vp training script**
- `test_vp_improved.py`: **🆕 Improved Vp testing with enhanced visualization**
- `plot_density_results.py`: Enhanced density prediction visualization
- `plot_vp_results.py`: Vp prediction visualization utilities
- `run_test.bat`: **🎮 Interactive test suite** (Windows)
- `*vp*.py`: Legacy Vp prediction scripts (deprecated)

## Key Features

### 🆕 Recent Improvements (2025)
- **Improved Vp Architecture**: Fixed sigmoid activation issues, proper scaling (40-400 μs/ft)
- **Enhanced Visualization**: Petrophysical-style log displays with multi-track layouts
- **Interactive Test Suite**: `run_test.bat` with 7 comprehensive testing options
- **Auto Data Detection**: Automatic discovery of A1.hdf5/A2.hdf5 files
- **Better Normalization**: Curve-specific normalization including log-transform for resistivity
- **Performance Monitoring**: Real-time training progress and validation metrics

### GPU/CPU Auto-Detection
The system automatically detects and uses available hardware:
- `utils.get_device()`: Intelligent device selection with GPU info
- `utils.load_checkpoint()`: Device-aware model loading
- Automatic fallback to CPU when GPU unavailable

### Model Training Configurations
- **Input curves**: GR (Gamma Ray), AC (Acoustic), CNL (Neutron), RLLD (Resistivity)
- **Output curves**: DEN (Density) for main models, AC for Vp prediction
- **Sequence handling**: 720-point windows with 640-point effective sequences
- **Data augmentation**: Random cropping and normalization options

### Hyperparameter Defaults
- **Density Prediction**: Learning rate 1e-5, Batch size 64, Patience 150 epochs
- **🆕 Improved Vp Prediction**: Learning rate 1e-4, Batch size 8, Patience 50 epochs
- **Legacy Vp Prediction**: Learning rate 1e-4, Batch size 16, Patience 100 epochs  
- **Dropout rates**: 0.1 across all model components
- **Training epochs**: 2000 (density), 200 (improved Vp), 30 (legacy Vp)

## Well Log Domain Context

### Input Curve Meanings
- **GR**: Gamma Ray (radioactivity, shale content indicator)
- **AC**: Acoustic/Sonic (travel time, porosity/lithology indicator)  
- **CNL**: Neutron (hydrogen content, porosity indicator)
- **RLLD**: Deep resistivity (fluid saturation indicator)
- **DEN**: Density (bulk density, lithology/porosity indicator)

### Model Applications
- **Primary**: Density curve prediction from other logs (mature, high accuracy)
- **🆕 Improved**: Sonic velocity prediction with proper scaling (40-400 μs/ft range)
- **Use cases**: Fill missing log sections, quality control, synthetic log generation, real-time logging optimization

## Development Notes

### Testing Approach
- No standardized test framework - uses custom evaluation scripts
- Model evaluation through RMSE, R², and visual comparison
- Performance monitoring via training loss curves and validation metrics

### Data Requirements
- Well log data in HDF5 format with standardized curve names
- Minimum sequence length of 720 points for effective training
- Proper depth registration and quality control of input curves

### Performance Expectations
- **GPU acceleration**: 4-5x faster than CPU inference
- **Density prediction**: RMSE <0.1 g/cm³, R² >0.9, training 2-4 hours (GPU)
- **🆕 Improved Vp prediction**: Proper 40-400 μs/ft scaling, training 10-20 minutes (GPU)
- **Legacy Vp prediction**: Poor performance, deprecated
- **Visualization**: Enhanced plots with petrophysical-style displays and statistical analysis