"""
Improved Vp prediction script with comprehensive log predictor visualization

Features:
- Enhanced Vp prediction with proper scaling and normalization
- Comprehensive log predictor plots showing input curves (GR, CNL, DEN, RLLD) alongside predictions
- Multi-panel visualization with individual well analysis
- Error analysis and statistical metrics
- Automatic handling of missing or inconsistent curve data
- Integration with existing comparison plotting functionality

Usage:
    python test_vp_improved.py --model_path path/to/model.pth

The script will generate:
1. Individual log predictor plots for each well (petrophysical log display style)
2. Comprehensive comparison plots with overall performance analysis
3. Statistical summaries and error analysis
"""
import os
import argparse
import torch
import numpy as np
import h5py
from torch.utils.data import DataLoader

from vp_model_improved import MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large, VpDataNormalizer
from utils import get_device, load_checkpoint, cal_RMSE, cal_R2
from las_processor import LASProcessor

try:
    import matplotlib.pyplot as plt
    import matplotlib.gridspec as gridspec
    from datetime import datetime
    from plot_vp_results import create_vp_comparison_plots
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️  Plotting functionality not available. Install matplotlib for enhanced visualization.")

def find_data_files():
    """
    Auto-detect the location of A1.hdf5 and A2.hdf5 files
    Excludes WELL_001.hdf5 to prevent potential data leakage
    """
    # Possible locations to search
    possible_paths = [
        # Current directory and parent
        '.',
        '..',
        # Your specific path
        r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        # Relative paths from code directory
        os.path.join('..'),
        os.path.join('..', '..'),
    ]

    found_files = []
    for base_path in possible_paths:
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')
        # REMOVED: well_path check to exclude WELL_001.hdf5 from testing
        # This ensures consistent dataset usage between training and testing

        if os.path.exists(a1_path) and os.path.exists(a2_path):
            found_files = [a1_path, a2_path]
            # REMOVED: WELL_001.hdf5 inclusion to prevent potential data leakage
            print(f"✅ Found data files in: {os.path.abspath(base_path)}")
            print(f"📁 Using files: A1.hdf5, A2.hdf5 (WELL_001.hdf5 excluded)")
            break

    return found_files

def validate_and_trim_curve_data(curves, target_length):
    """
    Validate and trim curve data to ensure consistent lengths
    """
    validated_curves = {}

    for curve_name in ['GR', 'CNL', 'DEN', 'RLLD', 'AC']:
        if curve_name in curves:
            curve_data = curves[curve_name]

            # Convert to numpy array if needed
            if not isinstance(curve_data, np.ndarray):
                curve_data = np.array(curve_data)

            # Handle different data shapes
            if curve_data.ndim > 1:
                curve_data = curve_data.flatten()

            # Trim or pad to target length
            if len(curve_data) >= target_length:
                validated_curves[curve_name] = curve_data[:target_length]
            elif len(curve_data) > 0:
                # Pad with the last value if curve is shorter
                padded_data = np.full(target_length, curve_data[-1])
                padded_data[:len(curve_data)] = curve_data
                validated_curves[curve_name] = padded_data
                print(f"⚠️  {curve_name} curve padded from {len(curve_data)} to {target_length} samples")
        else:
            # Create placeholder data if curve is missing
            validated_curves[curve_name] = np.zeros(target_length)
            print(f"⚠️  {curve_name} curve missing, using zeros")

    return validated_curves

def create_log_predictor_plot(curves, prediction, actual_vp, well_name, save_dir="../vp_improved_results"):
    """
    Create comprehensive log predictor visualization for a single well
    Shows input curves alongside predicted and actual Vp values
    """
    if not PLOTTING_AVAILABLE:
        print("⚠️  Matplotlib not available for log predictor plots")
        return None

    # Validate inputs
    if prediction is None or len(prediction) == 0:
        print(f"⚠️  No prediction data available for {well_name}")
        return None

    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Ensure curves is a dictionary
    if not isinstance(curves, dict):
        print(f"⚠️  Invalid curves data for {well_name}")
        return None

    # Validate and prepare curve data
    n_samples = len(prediction)
    validated_curves = validate_and_trim_curve_data(curves, n_samples)

    # Set up the figure with multiple tracks (subplots)
    fig = plt.figure(figsize=(16, 10))
    fig.suptitle(f'Well Log Analysis - {well_name}', fontsize=16, fontweight='bold')

    # Create a grid layout: 6 columns for different log tracks
    gs = gridspec.GridSpec(1, 6, figure=fig, width_ratios=[1, 1, 1, 1, 1.2, 1.2])

    # Determine depth/sample array
    depth = np.arange(n_samples)

    # Track 1: Gamma Ray (GR)
    ax1 = fig.add_subplot(gs[0])
    gr_data = validated_curves['GR']
    if np.any(gr_data != 0):  # Check if we have real data (not all zeros)
        ax1.plot(gr_data, depth, 'g-', linewidth=1.5, label='GR')
        ax1.fill_betweenx(depth, 0, gr_data, alpha=0.3, color='green')
    else:
        ax1.text(0.5, 0.5, 'GR\nNot Available', transform=ax1.transAxes,
                ha='center', va='center', fontsize=10, style='italic')

    ax1.set_ylabel('Sample Index', fontsize=12)
    ax1.set_xlabel('GR (API)', fontsize=10)
    ax1.set_title('Gamma Ray', fontsize=11, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.invert_yaxis()

    # Track 2: Neutron (CNL)
    ax2 = fig.add_subplot(gs[1])
    cnl_data = validated_curves['CNL']
    if np.any(cnl_data != 0):  # Check if we have real data
        ax2.plot(cnl_data, depth, 'b-', linewidth=1.5, label='CNL')
        ax2.fill_betweenx(depth, 0, cnl_data, alpha=0.3, color='blue')
    else:
        ax2.text(0.5, 0.5, 'CNL\nNot Available', transform=ax2.transAxes,
                ha='center', va='center', fontsize=10, style='italic')

    ax2.set_xlabel('CNL (%)', fontsize=10)
    ax2.set_title('Neutron', fontsize=11, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.invert_yaxis()
    ax2.set_yticklabels([])  # Remove y-axis labels for subsequent tracks

    # Track 3: Density (DEN)
    ax3 = fig.add_subplot(gs[2])
    den_data = validated_curves['DEN']
    if np.any(den_data != 0):  # Check if we have real data
        ax3.plot(den_data, depth, 'r-', linewidth=1.5, label='DEN')
        ax3.fill_betweenx(depth, den_data.min(), den_data, alpha=0.3, color='red')
    else:
        ax3.text(0.5, 0.5, 'DEN\nNot Available', transform=ax3.transAxes,
                ha='center', va='center', fontsize=10, style='italic')

    ax3.set_xlabel('DEN (g/cc)', fontsize=10)
    ax3.set_title('Density', fontsize=11, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.invert_yaxis()
    ax3.set_yticklabels([])

    # Track 4: Resistivity (RLLD) - Log scale
    ax4 = fig.add_subplot(gs[3])
    rlld_data = validated_curves['RLLD']
    if np.any(rlld_data != 0):  # Check if we have real data
        # Ensure positive values for log scale
        rlld_data = np.maximum(rlld_data, 0.1)
        ax4.semilogx(rlld_data, depth, 'm-', linewidth=1.5, label='RLLD')
        ax4.fill_betweenx(depth, 0.1, rlld_data, alpha=0.3, color='magenta')
    else:
        ax4.text(0.5, 0.5, 'RLLD\nNot Available', transform=ax4.transAxes,
                ha='center', va='center', fontsize=10, style='italic')

    ax4.set_xlabel('RLLD (Ω·m)', fontsize=10)
    ax4.set_title('Resistivity', fontsize=11, fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.invert_yaxis()
    ax4.set_yticklabels([])

    # Track 5: Sonic Velocity Comparison
    ax5 = fig.add_subplot(gs[4])

    # Plot actual Vp if available
    if actual_vp is not None and len(actual_vp) > 0:
        actual_trimmed = actual_vp[:n_samples] if len(actual_vp) >= n_samples else actual_vp
        depth_actual = np.arange(len(actual_trimmed))
        ax5.plot(actual_trimmed, depth_actual, 'b-', linewidth=2.5, label='Actual Vp', alpha=0.8)

    # Plot predicted Vp
    ax5.plot(prediction, depth, 'r--', linewidth=2.5, label='Predicted Vp', alpha=0.8)

    ax5.set_xlabel('Vp (μs/ft)', fontsize=10)
    ax5.set_title('Sonic Velocity\nComparison', fontsize=11, fontweight='bold')
    ax5.legend(loc='upper right', fontsize=9)
    ax5.grid(True, alpha=0.3)
    ax5.invert_yaxis()
    ax5.set_yticklabels([])

    # Track 6: Statistics and Error Analysis
    ax6 = fig.add_subplot(gs[5])

    if actual_vp is not None and len(actual_vp) > 0:
        # Calculate metrics
        actual_trimmed = actual_vp[:n_samples] if len(actual_vp) >= n_samples else actual_vp
        pred_trimmed = prediction[:len(actual_trimmed)]

        rmse = np.sqrt(np.mean((pred_trimmed - actual_trimmed)**2))
        mae = np.mean(np.abs(pred_trimmed - actual_trimmed))

        # Calculate R²
        ss_res = np.sum((actual_trimmed - pred_trimmed)**2)
        ss_tot = np.sum((actual_trimmed - np.mean(actual_trimmed))**2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

        # Plot residuals
        residuals = pred_trimmed - actual_trimmed
        depth_residuals = np.arange(len(residuals))

        ax6.plot(residuals, depth_residuals, 'k-', linewidth=1, alpha=0.7, label='Residuals')
        ax6.axvline(x=0, color='gray', linestyle='--', alpha=0.5)
        ax6.fill_betweenx(depth_residuals, 0, residuals, alpha=0.3,
                         color='red' if np.mean(residuals) > 0 else 'blue')

        # Add statistics text
        stats_text = f"""STATISTICS:
RMSE: {rmse:.2f} μs/ft
MAE: {mae:.2f} μs/ft
R²: {r2:.3f}

RANGES:
Actual: {actual_trimmed.min():.1f} - {actual_trimmed.max():.1f}
Predicted: {pred_trimmed.min():.1f} - {pred_trimmed.max():.1f}

SAMPLES: {len(pred_trimmed)}"""

        ax6.text(0.02, 0.98, stats_text, transform=ax6.transAxes, fontsize=8,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.8))
    else:
        ax6.text(0.5, 0.5, 'No Actual Data\nfor Comparison', transform=ax6.transAxes,
                ha='center', va='center', fontsize=10, style='italic')

    ax6.set_xlabel('Residuals (μs/ft)', fontsize=10)
    ax6.set_title('Error Analysis', fontsize=11, fontweight='bold')
    ax6.grid(True, alpha=0.3)
    ax6.invert_yaxis()
    ax6.set_yticklabels([])

    plt.tight_layout()

    # Save the plot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    safe_well_name = "".join(c for c in well_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    plot_file = os.path.join(save_dir, f'log_predictor_{safe_well_name}_{timestamp}.png')
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"📊 Log predictor plot saved: {plot_file}")

    plt.show()
    return plot_file

def predict_vp_improved(model_path, input_files=None, device=0):
    """
    Improved Vp prediction with proper scaling
    """
    device = get_device(device)
    normalizer = VpDataNormalizer()
    processor = LASProcessor()
    
    # Auto-detect input files if not provided
    if input_files is None:
        input_files = find_data_files()
        if not input_files:
            print("❌ No data files found! Please check file locations.")
            return []
    
    # Load improved model
    print(f"Loading improved Vp model from {model_path}")
    model = MWLT_Vp_Base()  # Adjust based on your trained model
    model = model.to(device)
    
    try:
        # Load checkpoint - try weights_only=True first, fallback if needed
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=True)
        except Exception as weights_only_error:
            print(f"⚠️  weights_only=True failed: {weights_only_error}")
            print("   Falling back to weights_only=False (ensure model is from trusted source)")
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)

        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print("✅ Model loaded successfully from checkpoint")
                if 'epoch' in checkpoint:
                    print(f"   Trained for {checkpoint['epoch']} epochs")
                if 'rmse' in checkpoint:
                    print(f"   Best RMSE: {checkpoint['rmse']:.4f}")
                if 'r2' in checkpoint:
                    print(f"   Best R²: {checkpoint['r2']:.4f}")
            else:
                print("❌ Error: Checkpoint missing 'model_state_dict'")
                print(f"   Available keys: {list(checkpoint.keys())}")
                print("   This indicates the model was saved incorrectly during training.")
                print("   Please retrain the model with the corrected training script.")
                return
        else:
            # Direct state_dict (older format)
            model.load_state_dict(checkpoint)
            print("✅ Model loaded successfully (direct state_dict)")

        model.eval()

    except Exception as e:
        print(f"❌ Error loading model: {e}")
        print(f"   Model path: {model_path}")
        print("   Possible solutions:")
        print("   1. Check if the model file exists and is not corrupted")
        print("   2. Verify the model architecture matches the saved model")
        print("   3. Retrain the model if it was saved incorrectly")
        return
    
    results = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found, skipping...")
            continue
            
        print(f"\nProcessing {file_path}...")
        
        # Load and process data
        curves = processor.process_hdf5_to_las_format(file_path)
        
        # Prepare input features with proper normalization
        input_features = []
        for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
            if curve_name in curves:
                data = torch.FloatTensor(curves[curve_name])
                if curve_name == 'RLLD':
                    # Log transform for resistivity
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized = (data - 1.0) / 2.0
                else:
                    stats = normalizer.input_stats[curve_name]
                    normalized = (data - stats['mean']) / stats['std']
                    normalized = torch.clamp(normalized, -3, 3) / 3
                input_features.append(normalized)
            else:
                print(f"Warning: {curve_name} not found, using zeros")
                input_features.append(torch.zeros(len(curves['AC'])))
        
        # Stack and prepare for model
        input_tensor = torch.stack(input_features).unsqueeze(0).to(device)  # [1, 4, seq_len]
        
        # Ensure correct sequence length
        target_length = 640
        if input_tensor.shape[2] > target_length:
            input_tensor = input_tensor[:, :, :target_length]
        elif input_tensor.shape[2] < target_length:
            # Pad if necessary
            padding = target_length - input_tensor.shape[2]
            input_tensor = torch.nn.functional.pad(input_tensor, (0, padding))
        
        # Predict
        with torch.no_grad():
            prediction_normalized = model(input_tensor)
            # UPDATED: Denormalize predictions to get physical units
            # This converts normalized model output back to μs/ft units
            prediction_tensor = torch.from_numpy(prediction_normalized.cpu().numpy())
            prediction_physical = normalizer.denormalize_vp(prediction_tensor)
            prediction = prediction_physical.numpy().flatten()
        
        # Get actual values for comparison
        actual_vp = curves.get('AC', np.zeros_like(prediction))
        if len(actual_vp) > len(prediction):
            actual_vp = actual_vp[:len(prediction)]
        elif len(actual_vp) < len(prediction):
            prediction = prediction[:len(actual_vp)]
        
        # Calculate metrics
        rmse = cal_RMSE(prediction, actual_vp)
        r2 = cal_R2(prediction, actual_vp)
        
        result = {
            'file': os.path.basename(file_path),
            'rmse': rmse,
            'r2': r2,
            'pred_range': [prediction.min(), prediction.max()],
            'actual_range': [actual_vp.min(), actual_vp.max()],
            'prediction': prediction,
            'actual': actual_vp,
            'curves': curves,  # Store raw curves for log predictor plotting
            'well_name': os.path.splitext(os.path.basename(file_path))[0]
        }
        results.append(result)
        
        print(f"  RMSE: {rmse:.2f}")
        print(f"  R²: {r2:.4f}")
        print(f"  Predicted range: [{prediction.min():.2f}, {prediction.max():.2f}]")
        print(f"  Actual range: [{actual_vp.min():.2f}, {actual_vp.max():.2f}]")
    
    return results

def create_all_log_predictor_plots(results, save_dir="../vp_improved_results"):
    """
    Create log predictor plots for all wells in the results
    """
    if not PLOTTING_AVAILABLE:
        print("⚠️  Matplotlib not available for log predictor plots")
        return []

    plot_files = []

    print("\n=== Creating Individual Well Log Predictor Plots ===")

    for i, result in enumerate(results):
        try:
            print(f"Creating log predictor plot for {result['well_name']} ({i+1}/{len(results)})...")

            plot_file = create_log_predictor_plot(
                curves=result['curves'],
                prediction=result['prediction'],
                actual_vp=result['actual'],
                well_name=result['well_name'],
                save_dir=save_dir
            )

            if plot_file:
                plot_files.append(plot_file)
                print(f"✅ Plot created: {os.path.basename(plot_file)}")

        except Exception as e:
            print(f"⚠️  Error creating log predictor plot for {result['well_name']}: {e}")
            continue

    if plot_files:
        print(f"\n📊 Successfully created {len(plot_files)} log predictor plots")
        print(f"📁 Plots saved in: {os.path.abspath(save_dir)}")
    else:
        print("\n⚠️  No log predictor plots were created")

    return plot_files

def create_comprehensive_visualization(results, save_dir="../vp_improved_results"):
    """
    Create both the existing comparison plots and new log predictor plots
    """
    plot_files = []

    try:
        # Create existing comparison plots
        print("Creating comprehensive comparison plots...")
        main_plot = create_vp_comparison_plots(results, save_dir)
        if main_plot:
            plot_files.append(main_plot)
            print(f"✅ Main comparison plot: {os.path.basename(main_plot)}")
    except Exception as e:
        print(f"⚠️  Error creating comparison plots: {e}")

    try:
        # Create individual log predictor plots
        log_plots = create_all_log_predictor_plots(results, save_dir)
        plot_files.extend(log_plots)
    except Exception as e:
        print(f"⚠️  Error creating log predictor plots: {e}")

    return plot_files

def main():
    parser = argparse.ArgumentParser(description="Improved Vp prediction")
    parser.add_argument("--model_path", type=str,
                       default=os.path.join('..', 'vp_improved_training', 'best_vp_improved_model.pth'),
                       help="Path to improved Vp model")
    parser.add_argument("--device", type=int, default=0, help="GPU device ID")
    parser.add_argument("--input_files", nargs='+', default=None,
                       help="Input files to process")
    
    args = parser.parse_args()
    
    print("=== Improved Vp Prediction Test ===")
    
    if not os.path.exists(args.model_path):
        print(f"❌ Model not found: {args.model_path}")
        print("Please train the improved model first:")
        print("  python train_vp_improved.py")
        return
    
    results = predict_vp_improved(args.model_path, args.input_files, args.device)
    
    if results:
        print("\n=== Summary ===")
        avg_rmse = np.mean([r['rmse'] for r in results])
        avg_r2 = np.mean([r['r2'] for r in results])
        print(f"Average RMSE: {avg_rmse:.2f}")
        print(f"Average R²: {avg_r2:.4f}")

        # Create comprehensive visualizations
        if PLOTTING_AVAILABLE:
            print("\n=== Creating Comprehensive Visualizations ===")
            try:
                plot_files = create_comprehensive_visualization(results)
                if plot_files:
                    print(f"\n✅ All visualizations created successfully!")
                    print(f"📊 Total plots generated: {len(plot_files)}")
                    print(f"📁 Plots location: {os.path.abspath('../vp_improved_results')}")

                    # List all created plots
                    print("\n📋 Generated plots:")
                    for plot_file in plot_files:
                        print(f"  • {os.path.basename(plot_file)}")
                else:
                    print("⚠️  No plots were created")
            except Exception as e:
                print(f"⚠️  Error creating visualizations: {e}")
                print("Continuing without enhanced visualization...")
        else:
            print("\n⚠️  Enhanced plotting not available. Install matplotlib for comprehensive visualization.")

        print("\n=== Performance Assessment ===")
        if avg_rmse < 50 and avg_r2 > 0.8:
            print("🎉 EXCELLENT performance! Model is working very well.")
        elif avg_rmse < 100 and avg_r2 > 0.5:
            print("✅ GOOD performance! Model shows promising results.")
        else:
            print("⚠️  Performance needs improvement. Consider:")
            print("  - More training data")
            print("  - Longer training (more epochs)")
            print("  - Different model architecture")
            print("  - Better data preprocessing")

        print(f"\n📈 Results saved with comprehensive visualization capabilities")
        print(f"🔍 Check the generated plots for detailed log analysis:")
        print(f"   • Comparison plots: Overall performance analysis")
        print(f"   • Log predictor plots: Individual well log analysis with input curves")
        print(f"   • Error analysis: Residuals and statistical metrics")

if __name__ == "__main__":
    main()
