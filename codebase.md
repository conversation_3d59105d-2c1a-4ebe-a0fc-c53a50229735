## Codebase Index

Generated: 2025-08-31

This document provides a high-level index of the repository, listing top-level folders and notable files. Paths are relative to the repo root. For brevity, cache folders and large data/result contents are summarized.

### Top-level items
- A1.hdf5 — Sample/input data
- A2.hdf5 — Sample/input data
- README.md — Project readme
- CLAUDE.md — Developer notes
- docs/enhanced-plotting-guide.md — Guide for plotting enhancements
- docs/vp-transformer-issues-and-fixes.md — Issues and fixes (includes sigmoid fix)
- docs/codebase-index.md — Consolidated index
- best_vp_model.pth — Saved model checkpoint
- inspect_model.py — Utility to inspect models
- simple_retrain.py — Simple retraining script
- test_enhanced_plotting.py — Tests for plotting
- test_training_setup.py — Tests for training setup

### Directories

- Init_transformer/
  - CLAUDE.md
  - README.md
  - TRAINING_FIX_ANALYSIS.md
  - core_codebase.md
  - current_phase_2.md
  - current_step_1.md
  - demo_curve_prediction.py
  - examples/ (contents not enumerated)
  - models/ (contents not enumerated)
  - next_phase_2.md
  - quick_integration_test.py
  - quick_phase2_validation.py
  - refactor_general.md
  - requirements.txt
  - test_phase1_implementation.py
  - test_phase2_implementation.py
  - vp_predictor/ (contents not enumerated)

- code/ — Core training, data, and plotting utilities
  - GPU_CPU_USAGE_GUIDE.md — Notes on GPU/CPU usage
  - VP_PREDICTION_GUIDE.md — Guide for VP prediction
  - VP_TRAINING_GUIDE.md — Guide for VP training
  - analyze_vp_results.py — Analyze VP prediction results
  - check_data_files.py — Validate/check data files
  - create_sample_data.py — Generate sample data
  - dataset.py — Dataset and data loaders
  - las_processor.py — LAS file processing
  - model.py — Model definitions
  - monitor_vp_training.py — Training monitor utilities
  - params_flops.txt — Model params/FLOPs info
  - plot_density_results.py — Plot density prediction results
  - plot_vp_results.py — Plot VP prediction results
  - prepare_vp_data.py — Prepare VP data
  - quick_train_vp.py — Quick train script
  - run_test.bat — Batch to run tests
  - simple_train_vp.py — Simple training script
  - test.py — Misc test
  - test_checkpoint.pt — Test checkpoint
  - test_density_plotting.py — Test density plots
  - test_device_detection.py — Test device detection
  - test_vp_improved.py — Test improved VP
  - test_vp_prediction.py — Test VP prediction
  - test_well.las — Sample LAS
  - train.py — Training entrypoint
  - train_vp_improved.py — Train improved VP model
  - train_vp_model.py — Train base VP model
  - train_vp_transfer.py — Transfer learning training
  - utils.py — Utility functions
  - vp_model_improved.py — Improved VP model
  - __pycache__/ (ignored)

- data_nonormal/
  - split_data.py — Data split utility

- data_normal/
  - split_data.py — Data split utility
  - val/ (validation data, contents not enumerated)

- data_vp_prediction/ — Prepared data for VP prediction
  - A1_vp_ready.hdf5
  - A1_vp_val.hdf5
  - A2_vp_ready.hdf5
  - A2_vp_val.hdf5

- density_prediction_results/ — Artifacts from density prediction
  - density_prediction_analysis_20250804_151457.png
  - density_prediction_report_20250804_151457.txt

- las_test_data/ — LAS/hdf5 test well data
  - WELL_001.hdf5
  - WELL_001.las
  - WELL_002.hdf5
  - WELL_002.las
  - WELL_003.hdf5
  - WELL_003.las

- result_base1/
  - pred_val/ (validation predictions)

- result_base_nonormal/ — Results for base model (non-normalized)
  - best_model.pth
  - cal_indicators.py
  - hyperparameter.txt
  - indicators.txt
  - loss.csv
  - pred_train/ (training predictions)
  - pred_val/ (validation predictions)

- result_base_normal/ — Results for base model (normalized)
  - best_model.pth
  - cal_indicators.py
  - hyperparameter.txt
  - indicators.txt
  - loss.csv
  - pred_train/
  - pred_val/

- result_large_nonormal/ — Results for larger model (non-normalized)
  - best_model.pth
  - cal_indicators.py
  - hyperparameter.txt
  - indicators.txt
  - loss.csv
  - pred_train/
  - pred_val/

- result_large_normal/ — Results for larger model (normalized)
  - best_model.pth
  - cal_indicators.py
  - hyperparameter.txt
  - indicators.txt
  - loss.csv
  - pred_train/
  - pred_val/

- result_small_nonormal/ — Results for small model (non-normalized)
  - best_model.pth
  - cal_indicators.py
  - hyperparameter.txt
  - indicators.txt
  - loss.csv
  - pred_train/
  - pred_val/

- result_small_normal/ — Results for small model (normalized)
  - best_model.pth
  - cal_indicators.py
  - hyperparameter.txt
  - indicators.txt
  - loss.csv
  - pred_train/
  - pred_val/

- simple_vp_data/
  - train/ (training data)
  - val/ (validation data)

- simple_vp_training/
  - best_simple_vp_model.pth

- test_plots/
  - log_predictor_TEST_INCOMPLETE_WELL_20250805_135207.png
  - log_predictor_TEST_WELL_001_20250805_135137.png

- vp_improved_base_training/
  - best_vp_improved_model.pth

- vp_improved_results/
  - vp_improved_analysis_20250804_145137.png
  - vp_improved_summary_20250804_145137.png

- vp_improved_training/
  - best_vp_improved_model.pth

- vp_prediction_results/
  - vp_pred_A1.hdf5
  - vp_pred_A2.hdf5
  - vp_pred_WELL_001.hdf5
  - vp_prediction_analysis.png
  - vp_prediction_report.txt

### Notes
- This is a structural index based on the current workspace. If you want a deeper index (e.g., listing functions/classes inside Python modules or expanding subdirectories like Init_transformer/examples and models), let me know and I can extend this document.

