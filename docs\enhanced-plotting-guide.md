# Enhanced Log Predictor Visualization Guide

## Overview

The `test_vp_improved.py` script has enhanced, petrophysical-style visualization for analyzing VpTransformer predictions.

## New Features

### 1. Individual Well Log Predictor Plots
- Multi-track display for GR, CNL, DEN, RLLD plus predictions
- Petrophysical style; residuals and stats included
- Separate files per well

### 2. Comprehensive Data Validation
- Automatic curve validation and length normalization
- Graceful handling of missing curves with warnings

### 3. Enhanced Visualization Layout
- 6-track display: GR | CNL | DEN | RLLD | Vp Comparison | Error/Stats
- Professional styling; RMSE/MAE/R² displayed

## Plot Structure

### Track Layout
```
┌─────┬─────┬─────┬─────┬─────────────┬─────────────┐
│ GR  │ CNL │ DEN │RLLD │ Vp Compare  │ Statistics  │
│     │     │     │     │             │ & Residuals │
│Green│Blue │ Red │Mag. │ Blue/Red    │ Error Plot  │
└─────┴─────┴─────┴─────┴─────────────┴─────────────┘
```

### Track Details
- GR (API, green, filled)
- CNL (percent, blue, filled)
- DEN (g/cc, red, filled)
- RLLD (Ω·m, magenta, log scale)
- Sonic Velocity Comparison: Actual (blue) vs Predicted (red dashed)
- Error/Stats: Residuals + RMSE, MAE, R²

## Usage

### Basic Usage
```bash
cd code
python test_vp_improved.py --model_path ../vp_improved_training/best_vp_improved_model.pth
```

### With Custom Files
```bash
python test_vp_improved.py --model_path model.pth --input_files ../A1.hdf5 ../A2.hdf5
```

## Output Files

- Individual: `log_predictor_<WELL>_YYYYMMDD_HHMMSS.png`
- Analysis: `vp_improved_analysis_YYYYMMDD_HHMMSS.png`
- Summary: `vp_improved_summary_YYYYMMDD_HHMMSS.png`
- Default directory: `../vp_improved_results/`

## Technical Implementation

Key functions in `test_vp_improved.py`:
- `create_log_predictor_plot()` — build per-well plots, handle missing data
- `validate_and_trim_curve_data()` — normalize curve lengths
- `create_all_log_predictor_plots()` — batch all wells
- `create_comprehensive_visualization()` — orchestrates full pipeline

### Error Handling
- Missing curves: auto detection + zero-padding + clear indicators
- Length mismatch: auto trim/pad with warnings
- Invalid data: validation with fallbacks

## Benefits

- Petrophysicists: familiar format, report-ready
- Data scientists: residuals and metrics for quick QA
- Integration: modular design, compatible with existing plotting

## Testing

```bash
python test_enhanced_plotting.py
```
- Covers basic plotting, missing curves, validation, error handling, file generation

## Requirements

```python
matplotlib>=3.3.0
numpy>=1.19.0
```
Optional: `pandas>=1.2.0`

## Backward Compatibility
- Existing `create_vp_comparison_plots()` still works; new features are additive

## Customization

- Colors in `create_log_predictor_plot()`:
```python
gr_color='green'; cnl_color='blue'; den_color='red'; rlld_color='magenta'
```
- Layout widths:
```python
gs = gridspec.GridSpec(1, 6, width_ratios=[1,1,1,1,1.2,1.2])
```
- Add custom metrics in the statistics section

## Future Enhancements
- Interactive plots, LAS export, multi-well correlation, custom scales

See also: README (Documentation section) and docs/vp-transformer-issues-and-fixes.md for core model changes affecting visualization.

