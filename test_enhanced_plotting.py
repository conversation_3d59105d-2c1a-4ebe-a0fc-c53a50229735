#!/usr/bin/env python3
"""
Test script for the enhanced log predictor plotting functionality
Demonstrates the new visualization capabilities in test_vp_improved.py
"""
import os
import sys
import numpy as np

# Add code directory to path
sys.path.append('code')

def test_log_predictor_plotting():
    """
    Test the enhanced log predictor plotting functionality
    """
    print("=== Testing Enhanced Log Predictor Plotting ===")
    
    try:
        # Import the enhanced test module
        from test_vp_improved import (
            create_log_predictor_plot, 
            validate_and_trim_curve_data,
            PLOTTING_AVAILABLE
        )
        print("✅ Successfully imported enhanced plotting functions")
        
        if not PLOTTING_AVAILABLE:
            print("❌ Matplotlib not available - plotting functionality disabled")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Create synthetic test data
    print("\n📊 Creating synthetic test data...")
    n_samples = 640
    depth = np.arange(n_samples)
    
    # Synthetic well log curves
    test_curves = {
        'GR': 50 + 30 * np.sin(depth / 100) + np.random.normal(0, 5, n_samples),
        'CNL': 15 + 10 * np.cos(depth / 80) + np.random.normal(0, 2, n_samples),
        'DEN': 2.3 + 0.3 * np.sin(depth / 120) + np.random.normal(0, 0.05, n_samples),
        'RLLD': 10 * np.exp(np.sin(depth / 150)) + np.random.normal(0, 2, n_samples),
        'AC': 200 + 50 * np.sin(depth / 90) + np.random.normal(0, 10, n_samples)
    }
    
    # Ensure positive values for resistivity
    test_curves['RLLD'] = np.maximum(test_curves['RLLD'], 0.1)
    
    # Synthetic predictions (with some error)
    actual_vp = test_curves['AC']
    predicted_vp = actual_vp + np.random.normal(0, 15, n_samples)  # Add prediction error
    
    print(f"✅ Created synthetic data with {n_samples} samples")
    print(f"   GR range: {test_curves['GR'].min():.1f} - {test_curves['GR'].max():.1f} API")
    print(f"   CNL range: {test_curves['CNL'].min():.1f} - {test_curves['CNL'].max():.1f} %")
    print(f"   DEN range: {test_curves['DEN'].min():.2f} - {test_curves['DEN'].max():.2f} g/cc")
    print(f"   RLLD range: {test_curves['RLLD'].min():.1f} - {test_curves['RLLD'].max():.1f} Ω·m")
    print(f"   Vp range: {actual_vp.min():.1f} - {actual_vp.max():.1f} μs/ft")
    
    # Test curve validation function
    print("\n🔍 Testing curve validation...")
    validated_curves = validate_and_trim_curve_data(test_curves, n_samples)
    print(f"✅ Curve validation successful - {len(validated_curves)} curves validated")
    
    # Test log predictor plot creation
    print("\n📈 Testing log predictor plot creation...")
    try:
        plot_file = create_log_predictor_plot(
            curves=test_curves,
            prediction=predicted_vp,
            actual_vp=actual_vp,
            well_name="TEST_WELL_001",
            save_dir="test_plots"
        )
        
        if plot_file and os.path.exists(plot_file):
            print(f"✅ Log predictor plot created successfully!")
            print(f"📊 Plot saved: {plot_file}")
            print(f"📁 File size: {os.path.getsize(plot_file) / 1024:.1f} KB")
            return True
        else:
            print("❌ Plot file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error creating log predictor plot: {e}")
        return False

def test_missing_curves():
    """
    Test handling of missing or incomplete curve data
    """
    print("\n=== Testing Missing Curve Handling ===")
    
    try:
        from test_vp_improved import create_log_predictor_plot, PLOTTING_AVAILABLE
        
        if not PLOTTING_AVAILABLE:
            print("⚠️  Skipping test - matplotlib not available")
            return True
            
        # Create test data with missing curves
        n_samples = 320
        incomplete_curves = {
            'GR': np.random.uniform(20, 150, n_samples),
            # 'CNL': missing
            'DEN': np.random.uniform(2.0, 2.8, n_samples//2),  # Shorter curve
            'RLLD': np.random.uniform(1, 100, n_samples),
            'AC': np.random.uniform(150, 300, n_samples)
        }
        
        predicted_vp = np.random.uniform(160, 280, n_samples)
        actual_vp = incomplete_curves['AC']
        
        print(f"📊 Testing with incomplete data:")
        print(f"   GR: {len(incomplete_curves['GR'])} samples")
        print(f"   CNL: Missing")
        print(f"   DEN: {len(incomplete_curves['DEN'])} samples (short)")
        print(f"   RLLD: {len(incomplete_curves['RLLD'])} samples")
        
        plot_file = create_log_predictor_plot(
            curves=incomplete_curves,
            prediction=predicted_vp,
            actual_vp=actual_vp,
            well_name="TEST_INCOMPLETE_WELL",
            save_dir="test_plots"
        )
        
        if plot_file:
            print("✅ Successfully handled missing/incomplete curves")
            return True
        else:
            print("❌ Failed to handle missing curves")
            return False
            
    except Exception as e:
        print(f"❌ Error testing missing curves: {e}")
        return False

def main():
    """
    Run all tests for the enhanced plotting functionality
    """
    print("🧪 Testing Enhanced Log Predictor Plotting Functionality")
    print("=" * 60)
    
    # Test basic functionality
    test1_passed = test_log_predictor_plotting()
    
    # Test error handling
    test2_passed = test_missing_curves()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY:")
    print(f"   Basic plotting: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Missing curves: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests PASSED! Enhanced plotting functionality is working correctly.")
        print("📁 Check the 'test_plots' directory for generated test plots.")
    else:
        print("\n⚠️  Some tests FAILED. Check the error messages above.")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
