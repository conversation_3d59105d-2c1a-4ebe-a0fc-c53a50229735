# Integration Guide (VpTransformer)

This guide shows how to integrate the improved VpTransformer into external apps.

## Essentials
- code/vp_model_improved.py — improved architecture
- code/model.py — base components
- code/utils.py — device and checkpoint helpers
- code/las_processor.py — data IO and preparation

## Minimal Predictor Wrapper
```python
import torch, numpy as np
from vp_model_improved import MWLT_Vp_Base, VpDataNormalizer
from utils import get_device, load_checkpoint
from las_processor import LASProcessor

class VpPredictor:
    def __init__(self, model_path, device_id=0):
        self.device = get_device(device_id)
        self.normalizer = VpDataNormalizer()
        self.processor = LASProcessor()
        self.model = MWLT_Vp_Base().to(self.device).eval()
        state, _, _ = load_checkpoint(model_path, self.device)
        self.model.load_state_dict(state)

    def predict_from_curves(self, gr, cnl, den, rlld):
        curves = {'GR': gr, 'CNL': cnl, 'DEN': den, 'RLLD': rlld}
        x, _ = self.processor.prepare_for_prediction(curves)
        L = 640
        if x.shape[1] > L: x = x[:, :L]
        if x.shape[1] < L: x = np.pad(x, ((0,0),(0,L-x.shape[1])))
        with torch.no_grad():
            y = self.model(torch.FloatTensor(x).unsqueeze(0).to(self.device))
        return y.cpu().numpy().flatten()

    def predict_from_file(self, path):
        if path.endswith('.hdf5'):
            curves = self.processor.process_hdf5_to_las_format(path)
        elif path.endswith('.las'):
            curves = self.processor.read_las_file(path)
        else:
            raise ValueError('Unsupported file format')
        return self.predict_from_curves(curves.get('GR'), curves.get('CNL'), curves.get('DEN'), curves.get('RLLD'))
```

## Architecture (Mermaid)
```mermaid
graph TD
  A[GR,CNL,DEN,RLLD]-->B[Input_Embedding]
  B-->C[Transformer Encoders]
  C-->D[VpDecoder]
  D-->E[Normalized output]-->F[Denormalize to Vp]
```

## Troubleshooting
- Predictions stuck near ~40 μs/ft: use improved model and denormalization
- Range coverage poor: verify training normalization and updated decoder
- Old checkpoints: retrain with new architecture

See also:
- docs/vp-transformer-issues-and-fixes.md
- docs/enhanced-plotting-guide.md
- docs/codebase-index.md

