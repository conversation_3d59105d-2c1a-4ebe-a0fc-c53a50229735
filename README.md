# MWLT - Machine Learning Well Log Transformer

A transformer-based deep learning system for predicting well log properties, specifically designed for density (DEN) and sonic velocity (Vp/AC) prediction from input well log curves.

## 🎯 Project Overview

The Machine Learning Well Log Transformer (MWLT) uses transformer architecture combined with ResNet-based feature extraction to predict missing or poor-quality well log curves from available measurements. The system supports:

- **Primary Task**: Density (DEN) prediction from GR, AC, CNL, RLLD curves
- **Secondary Task**: Sonic velocity (Vp/AC) prediction with improved architecture
- **GPU/CPU Auto-detection**: Automatic hardware optimization
- **Multiple Model Sizes**: Small, Base, and Large variants

## 📁 Codebase Structure

```
Transformer/
├── code/                           # Main source code directory
│   ├── model.py                    # Core transformer architecture
│   ├── train.py                    # Main density prediction training
│   ├── test.py                     # Model evaluation and inference
│   ├── dataset.py                  # Data loading and preprocessing
│   ├── utils.py                    # Utilities (device detection, early stopping)
│   ├── las_processor.py            # LAS file processing utilities
│   │
│   ├── vp_model_improved.py        # 🆕 Complete Vp prediction module
│   ├── train_vp_improved.py        # 🆕 Improved Vp training script
│   ├── test_vp_improved.py         # 🆕 Improved Vp testing
│   │
│   ├── run_test.bat                # 🎮 Interactive test suite
│   └── [legacy vp scripts...]      # Original Vp prediction attempts
│
├── data_normal/                    # Normalized training data
│   ├── train/                      # Training HDF5 files
│   └── val/                        # Validation HDF5 files
│
├── data_nonormal/                  # Non-normalized training data
├── data_vp_prediction/             # Vp-specific training data
├── las_test_data/                  # Sample LAS and HDF5 files
│
├── result_base_normal/             # Base model training results
├── vp_improved_training/           # 🆕 Improved Vp model results
├── simple_vp_training/             # Legacy Vp training results
│
├── A1.hdf5, A2.hdf5               # Main well log data files
└── README.md                       # This file
```

## 🔬 ML Model Architecture

### Core Components

#### 1. Input Embedding (`Input_Embedding`)
- **ResNet-based feature extraction** using 1D convolutions
- Converts raw well log curves to high-dimensional features
- Architecture: `[B, C, L] → [B, feature_num, L]`

#### 2. Positional Encoding (`PositionalEncoding`)  
- Sinusoidal position embeddings for sequence awareness
- Handles variable sequence lengths (up to 720 points)
- Format: `[B, L, feature_num]`

#### 3. Transformer Encoder (`TransformerBlock`)
- Multi-head self-attention mechanism
- Layer normalization and residual connections
- Configurable depth and attention heads

#### 4. Decoder
- **Standard Decoder**: For density prediction with sigmoid activation
- **VpDecoder**: 🆕 Specialized for sonic velocity with proper scaling (40-400 μs/ft)

### Model Variants

| Model | ResNet Blocks | Transformer Layers | Attention Heads | Features |
|-------|---------------|-------------------|-----------------|----------|
| **Small** | 2 | 2 | 2 | 64 |
| **Base** | 4 | 4 | 4 | 64 |
| **Large** | 6 | 6 | 8 | 128 |

### Data Flow

```
Input Curves [B,4,720] 
    ↓ Input_Embedding
Feature Maps [B,64,720]
    ↓ Transpose
Transformer Input [B,720,64]
    ↓ PositionalEncoding + TransformerBlock  
Context Features [B,720,64]
    ↓ Transpose
CNN Format [B,64,720]
    ↓ Decoder
Output Curves [B,1,720]
```

## 📊 Dataset Format

### Input Curves (Features)
- **GR**: Gamma Ray (radioactivity, shale content) - API units
- **AC**: Acoustic/Sonic (travel time, porosity) - μs/ft  
- **CNL**: Neutron (hydrogen content, porosity) - %
- **RLLD**: Deep Resistivity (fluid saturation) - ohm⋅m

### Output Curves (Targets)
- **DEN**: Bulk Density - g/cm³ (Primary task)
- **AC**: Acoustic/Sonic - μs/ft (Vp prediction task)

### Data Format
- **File Type**: HDF5 (`.hdf5`) or LAS (`.las`)
- **Sequence Length**: 720 points → 640 effective (with augmentation)
- **Normalization**: Curve-specific with statistical and physical constraints
- **Augmentation**: Sliding windows with 50% overlap

### Expected Data Files
- `A1.hdf5`: Primary well log dataset
- `A2.hdf5`: Secondary well log dataset
- Training data in `data_normal/train/` and `data_normal/val/`

## 🚀 Getting Started

### Prerequisites
```bash
pip install torch torchvision numpy pandas h5py scikit-learn matplotlib
pip install thop  # For model profiling
```

### Quick Start Guide

#### 1. Interactive Test Suite (Recommended)
```bash
cd code/
run_test.bat  # Windows
```

**Available Options:**
- **Option 1**: Standard density prediction test
- **Option 2**: Legacy Vp prediction (poor performance)  
- **Option 3**: 🆕 **Improved Vp prediction with plotting** (Recommended)
- **Option 4**: Device detection test
- **Option 5**: Quick Vp training (legacy)
- **Option 6**: 🆕 **Train improved Vp model** (Recommended)

#### 2. Manual Training

**For Density Prediction:**
```bash
cd code/
python train.py --model_type base --epochs 2000 --batch_size 64 --save_path ../result_base_normal
```

**For 🆕 Improved Vp Prediction (Recommended):**
```bash
cd code/
python train_vp_improved.py  # Fixed architecture with proper scaling
# or
python vp_model_improved.py --mode train --model_type base  # All-in-one interface
```

**Legacy Vp Training (Deprecated):**
```bash
python simple_train_vp.py  # Poor performance, use improved version above
```

#### 3. Testing Models

**Density Prediction:**
```bash
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth"
```

**🆕 Improved Vp Prediction (Recommended):**
```bash
python test_vp_improved.py  # Auto data detection + enhanced visualization
# or  
python vp_model_improved.py --mode predict --model_path model.pth --input_file A1.hdf5
```

**Legacy Vp Testing (Deprecated):**
```bash
python test_vp_prediction.py  # Poor performance, use improved version above
```

### Key Entry Points

| Task | Recommended Script | Status | Description |
|------|-------------------|--------|-------------|
| **🚀 Start Here** | `run_test.bat` | ✅ Ready | Interactive test suite with 7 options |
| **Density Training** | `train.py` | ✅ Mature | Main density prediction (proven) |
| **🆕 Vp Training** | `train_vp_improved.py` | ✅ Improved | Fixed Vp model with proper scaling |
| **Evaluation** | `test.py` / `test_vp_improved.py` | ✅ Enhanced | Model testing with visualization |
| **All-in-One** | `vp_model_improved.py` | ✅ Complete | Full Vp pipeline (train/predict/visualize) |
| **Legacy Vp** | `simple_train_vp.py` | ❌ Deprecated | Poor performance, use improved version |

## ⚙️ Configuration

### Training Parameters

**Density Prediction:**
- Learning Rate: `1e-5`
- Batch Size: `64`
- Epochs: `2000`
- Patience: `150`

**🆕 Vp Prediction (Improved):**
- Learning Rate: `1e-4`  
- Batch Size: `8`
- Epochs: `200`
- Patience: `50`
- **Key Fix**: Removed sigmoid activation for proper scaling

### Hardware Requirements
- **GPU**: CUDA-compatible (recommended, 4-5x faster)
- **CPU**: Fallback mode (functional but slower)
- **Memory**: 8GB+ RAM, 4GB+ GPU memory
- **Storage**: 2GB+ for datasets and models

## 🔧 Key Features

### Auto-Detection Systems
- **Hardware**: Automatic GPU/CPU selection with device info
- **Data Files**: Auto-discovery of A1.hdf5/A2.hdf5 in multiple locations
- **Model Loading**: Device-aware checkpoint loading

### 🆕 Improved Vp Architecture
- **🔧 Sigmoid Fix**: Removed artificial range constraints that limited output
- **📏 Proper Scaling**: Output properly scaled to realistic Vp range (40-400 μs/ft)
- **📊 Better Normalization**: Curve-specific normalization including log-transform for resistivity
- **🎯 Enhanced Data**: Overlapping windows for more training samples
- **📈 Smart Loss**: Physics-aware loss function with constraint penalties
- **🎨 Visualization**: Petrophysical-style 6-track log displays

### Quality Assurance
- **Early Stopping**: Prevents overfitting with patience-based monitoring
- **Metrics Tracking**: RMSE, R², and training/validation curves
- **Checkpointing**: Automatic best model saving

## 📈 Performance Expectations

### Density Prediction
- **RMSE**: <0.1 g/cm³ on normalized data
- **R²**: >0.9 on validation set
- **Training Time**: 2-4 hours (GPU), 8-12 hours (CPU)

### 🆕 Vp Prediction (Improved)
- **RMSE**: Significantly improved over legacy models (no more artificial bounds)
- **Range**: Properly scaled to realistic 40-400 μs/ft (fixed sigmoid issue)
- **Training Time**: 10-20 minutes (GPU), 1-2 hours (CPU)
- **Visualization**: Professional petrophysical-style log displays

## 🛠️ Development Notes

### Testing Framework
- No standardized test framework - uses custom evaluation scripts
- Performance monitoring through training loss curves and validation metrics
- Visual inspection through plotting utilities

### Data Pipeline
- Supports both HDF5 and LAS file formats
- Automatic curve name standardization
- Handles missing data with zero-padding
- Quality control through statistical validation

### Model Saving Format
```python
checkpoint = {
    "model_state_dict": model.state_dict(),
    "optimizer_state_dict": optimizer.state_dict(), 
    "loss": validation_loss,
    "epoch": current_epoch,
    "rmse": validation_rmse,
    "r2": validation_r2
}
```

## 🚨 Troubleshooting

### Common Issues

**1. "No module named 'thop'"**
```bash
pip install thop
```

**2. "Could not find A1.hdf5 and A2.hdf5"**
- Ensure data files are in the main Transformer directory
- Check the auto-detection paths in console output

**3. "CUDA out of memory"**
- Reduce batch size: `--batch_size 4` or `--batch_size 2`
- Use CPU mode if necessary

**4. "Tensor dimension mismatch"**
- ✅ **FIXED** in the improved models (`vp_model_improved.py`)
- Use improved scripts instead of legacy ones

### Performance Issues
- **Poor Vp predictions**: ✅ **SOLVED** - Use the improved model (`train_vp_improved.py`)
- **Sigmoid range limiting**: ✅ **FIXED** - Removed artificial output constraints
- **Slow training**: Check GPU availability with device detection test
- **Poor convergence**: Verify data normalization and learning rate

## 📚 References

### Well Log Domain Context
- **GR**: Shale content indicator (0-200 API)
- **AC**: Porosity/lithology indicator (40-400 μs/ft)
- **CNL**: Porosity indicator (0-60%)
- **RLLD**: Fluid saturation indicator (0.1-1000 ohm⋅m)
- **DEN**: Lithology/porosity indicator (1.5-3.0 g/cm³)

### Model Applications
- Fill missing log sections
- Quality control and validation
- Synthetic log generation
- Real-time logging optimization

---

---

## 🆕 **What's New in 2025**

### Major Improvements
- **🔧 Fixed Vp Architecture**: Resolved sigmoid activation issues that limited output range
- **📊 Enhanced Visualization**: Petrophysical-style log displays with 6-track layouts
- **🎮 Interactive Test Suite**: Comprehensive `run_test.bat` with 7 testing options
- **🔍 Auto Data Detection**: Smart discovery of A1.hdf5/A2.hdf5 files across directories
- **📈 Better Performance**: Improved Vp models with proper 40-400 μs/ft scaling
- **📋 Real-time Monitoring**: Training progress with validation metrics and early stopping

### Quick Results Preview
- **Density Prediction**: Mature model with RMSE <0.1 g/cm³, R² >0.9
- **Improved Vp Prediction**: Now properly scaled with professional visualization
- **Training Speed**: 10-20 minutes (GPU) for Vp, 2-4 hours for density

---

## 📚 Documentation
- [Developer Guide](docs/developer-guide.md)
- [Integration Guide](docs/integration-guide.md)
- [VpTransformer Issues & Fixes](docs/vp-transformer-issues-and-fixes.md)
- [Enhanced Plotting Guide](docs/enhanced-plotting-guide.md)
- [Codebase Index](docs/codebase-index.md)

**🚀 Getting Started**: Run `code/run_test.bat` and select **Option 6** to train the improved Vp model, then **Option 3** to test it with enhanced visualization!